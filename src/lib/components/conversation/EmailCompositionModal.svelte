<!--
	@component EmailCompositionModal
	
	A modal dialog for composing emails (reply, reply all, forward, or new email).
	Provides a full-featured email composition interface with recipient management,
	subject editing, rich text content, and attachment support.
	
	@example
	```svelte
	<EmailCompositionModal
		bind:isOpen={compositionModalOpen}
		{compositionData}
		on:send={handleEmailSend}
		on:cancel={handleCompositionCancel}
	/>
	```
-->
<script lang="ts">
	import { createEventDispatcher, onDestroy } from 'svelte';
	import { fade, scale } from 'svelte/transition';
	import {
		CloseOutline,
		PaperPlaneOutline,
		ChevronDownOutline,
		ChevronUpOutline
	} from 'flowbite-svelte-icons';
	import RichTextArea from '$lib/components/common/RichTextArea.svelte';
	import EmailAutoComplete from '$lib/components/common/EmailAutoComplete.svelte';

	// Types
	interface CompositionData {
		action: 'reply' | 'replyAll' | 'forward' | 'compose';
		originalMessageId?: number;
		content: string;
		subject: string;
		to: string[];
		cc: string[];
		bcc: string[];
		originalMessage?: {
			sender: { name: string; email: string };
			timestamp: string;
			body: string;
		};
	}

	// Props
	export let isOpen: boolean = false;
	export let compositionData: CompositionData | null = null;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		send: {
			action: string;
			originalMessageId?: number;
			content: string;
			subject: string;
			to: string[];
			cc: string[];
			bcc: string[];
		};
		cancel: void;
		close: void;
	}>();

	// Component state
	let modalElement: HTMLDivElement;
	let showCCBCC: boolean = false;
	let isSending: boolean = false;
	let formErrors: { [key: string]: string } = {};

	// Form data - initialize with empty values
	let formData = {
		content: '',
		subject: '',
		to: [] as string[],
		cc: [] as string[],
		bcc: [] as string[]
	};

	// Initialize form data when composition data changes
	$: if (compositionData && isOpen) {
		formData = {
			content: compositionData.content || '',
			subject: compositionData.subject || '',
			to: [...(compositionData.to || [])],
			cc: [...(compositionData.cc || [])],
			bcc: [...(compositionData.bcc || [])]
		};
		showCCBCC = formData.cc.length > 0 || formData.bcc.length > 0;
		formErrors = {};
	}

	// Get modal title based on action
	$: modalTitle = compositionData ? getModalTitle(compositionData.action) : 'Compose Email';

	function getModalTitle(action: string): string {
		switch (action) {
			case 'reply': return 'Reply';
			case 'replyAll': return 'Reply All';
			case 'forward': return 'Forward';
			case 'compose': return 'Compose Email';
			default: return 'Compose Email';
		}
	}

	// Handle modal close
	function closeModal() {
		if (isSending) return; // Prevent closing while sending
		
		isOpen = false;
		dispatch('close');
		dispatch('cancel');
	}

	// Handle backdrop click
	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			closeModal();
		}
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && !isSending) {
			closeModal();
		}
	}

	// Validate form
	function validateForm(): boolean {
		formErrors = {};
		let isValid = true;

		// Validate recipients
		if (formData.to.length === 0) {
			formErrors.to = 'At least one recipient is required';
			isValid = false;
		}

		// Validate subject
		if (!formData.subject.trim()) {
			formErrors.subject = 'Subject is required';
			isValid = false;
		}

		// Validate content
		if (!formData.content.trim() || formData.content.trim() === '<p></p>') {
			formErrors.content = 'Message content is required';
			isValid = false;
		}

		return isValid;
	}

	// Handle send
	async function handleSend() {
		if (isSending || !validateForm()) return;

		isSending = true;

		try {
			dispatch('send', {
				action: compositionData?.action || 'compose',
				originalMessageId: compositionData?.originalMessageId,
				content: formData.content,
				subject: formData.subject,
				to: formData.to,
				cc: formData.cc,
				bcc: formData.bcc
			});

			// Close modal after successful send
			setTimeout(() => {
				isOpen = false;
				isSending = false;
			}, 100);
		} catch (error) {
			console.error('Error sending email:', error);
			isSending = false;
		}
	}

	// Handle content update from rich text editor
	function handleContentUpdate(event: CustomEvent) {
		formData.content = event.detail.editor.getHTML();
	}

	// Toggle CC/BCC visibility
	function toggleCCBCC() {
		showCCBCC = !showCCBCC;
	}

	// Handle recipient changes
	function handleToChange(event: CustomEvent) {
		formData.to = event.detail.contacts.map((c: any) => c.email);
		if (formErrors.to) delete formErrors.to;
	}

	function handleCCChange(event: CustomEvent) {
		formData.cc = event.detail.contacts.map((c: any) => c.email);
	}

	function handleBCCChange(event: CustomEvent) {
		formData.bcc = event.detail.contacts.map((c: any) => c.email);
	}

	// Handle subject change
	function handleSubjectChange(event: Event) {
		const target = event.target as HTMLInputElement;
		formData.subject = target.value;
		if (formErrors.subject) delete formErrors.subject;
	}

	// Manage body scroll when modal opens/closes
	$: if (typeof document !== 'undefined') {
		if (isOpen) {
			document.body.classList.add('modal-open');
		} else {
			document.body.classList.remove('modal-open');
		}
	}

	onDestroy(() => {
		if (typeof document !== 'undefined') {
			document.body.classList.remove('modal-open');
		}
	});
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<!-- Modal backdrop -->
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
	<div
		class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
		on:click={handleBackdropClick}
		role="dialog"
		aria-modal="true"
		aria-labelledby="email-modal-title"
	>
		<!-- Modal content -->
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div
			bind:this={modalElement}
			class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
			on:click|stopPropagation
			role="document"
		>
			<!-- Modal header -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200">
				<h2 id="email-modal-title" class="text-lg font-semibold text-gray-900">
					{modalTitle}
				</h2>
				<button
					class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
					on:click={closeModal}
					disabled={isSending}
					aria-label="Close modal"
				>
					<CloseOutline class="w-5 h-5" />
				</button>
			</div>

			<!-- Modal body -->
			<div class="flex-1 overflow-y-auto p-4">
				<!-- Recipients section -->
				<!-- To field -->
				<div class="flex items-center">
					<label for="compose-to" class="block text-sm font-medium text-gray-400 w-20">
						To <span class="text-red-500">*</span>
					</label>
					<div class="flex-1">
						<EmailAutoComplete
							id="compose-to"
							value={formData.to.join(', ')}
							placeholder="Enter recipient email addresses..."
							multiple={true}
							on:change={handleToChange}
						/>
					</div>
					{#if formErrors.to}
						<p class="mt-1 text-sm text-red-600">{formErrors.to}</p>
					{/if}
					<!-- CC/BCC toggle button -->
					<div class="flex items-center">
						<button
							type="button"
							class="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1"
							on:click={toggleCCBCC}
						>
							{#if showCCBCC}
								<ChevronUpOutline class="w-4 h-4" />
								<span>Hide Cc/Bcc</span>
							{:else}
								<ChevronDownOutline class="w-4 h-4" />
								<span>Show Cc/Bcc</span>
							{/if}
						</button>
					</div>
				</div>


				<!-- CC and BCC fields -->
				{#if showCCBCC}
					<div>
						<!-- CC field -->
						<div class="flex items-center">
							<label for="compose-cc" class="block text-sm font-medium text-gray-400 w-20">
								Cc
							</label>
							<div class="flex-1">
								<EmailAutoComplete
									id="compose-cc"
									value={formData.cc.join(', ')}
									placeholder="Enter CC recipients..."
									multiple={true}
									on:change={handleCCChange}
								/>
							</div>
						</div>

						<!-- BCC field -->
						<div class="flex items-center">
							<label for="compose-bcc" class="block text-sm font-medium text-gray-400 w-20">
								Bcc
							</label>
							<div class="flex-1">
								<EmailAutoComplete
									id="compose-bcc"
									value={formData.bcc.join(', ')}
									placeholder="Enter BCC recipients..."
									multiple={true}
									on:change={handleBCCChange}
								/>
							</div>
						</div>
					</div>
				{/if}

				<!-- Subject field -->
				<div class="flex items-center">
					<label for="compose-subject" class="block text-sm font-medium text-gray-400 w-20">
						Subject <span class="text-red-500">*</span>
					</label>
					<input
						id="compose-subject"
						type="text"
						class="flex-1 w-full px-3 py-2 focus:outline-none focus:ring-0 border-0 text-sm text-gray-900"
						value={formData.subject}
						on:input={handleSubjectChange}
						placeholder="Enter email subject..."
						disabled={isSending}
					/>
					{#if formErrors.subject}
						<p class="mt-1 text-sm text-red-600">{formErrors.subject}</p>
					{/if}
				</div>

				<!-- Content editor -->
				<div class="mt-3">
					<label for="compose-content" class="block text-sm font-medium text-gray-400 mb-1">
						Message <span class="text-red-500">*</span>
					</label>
					<div class="border border-gray-300 rounded-md overflow-hidden" id="compose-content">
						<RichTextArea
							content={formData.content}
							on:update={handleContentUpdate}
						/>
					</div>
					{#if formErrors.content}
						<p class="mt-1 text-sm text-red-600">{formErrors.content}</p>
					{/if}
				</div>

				<!-- Attachments section (placeholder for future implementation) -->
				<!-- <div>
					<label class="block text-sm font-medium text-gray-700 mb-1">
						Attachments:
					</label>
					<div class="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
						<PaperClipOutline class="w-8 h-8 text-gray-400 mx-auto mb-2" />
						<p class="text-sm text-gray-500">Drag and drop files here or click to browse</p>
					</div>
				</div> -->
			</div>

			<!-- Modal footer -->
			<div class="flex items-center justify-between p-4 border-t border-gray-200">
				<div class="flex items-center space-x-2">
					<!-- Future: Attachment button, formatting options, etc. -->
				</div>
				
				<div class="flex items-center space-x-3">
					<button
						type="button"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
						on:click={closeModal}
						disabled={isSending}
					>
						Cancel
					</button>
					<button
						type="button"
						class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
						on:click={handleSend}
						disabled={isSending}
					>
						{#if isSending}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							Sending...
						{:else}
							<PaperPlaneOutline class="w-4 h-4 mr-2" />
							Send
						{/if}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	/* Prevent body scroll when modal is open */
	:global(body.modal-open) {
		overflow: hidden;
	}
</style>
